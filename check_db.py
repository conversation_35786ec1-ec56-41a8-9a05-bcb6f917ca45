#!/usr/bin/env python3
from models.database import engine, Base
from sqlalchemy import text
import os

def check_database():
    with engine.connect() as conn:
        # Check existing tables
        result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
        tables = [row[0] for row in result]
        print("Existing tables:", tables)
        
        # Check if users table exists
        if 'users' in tables:
            print("✓ Users table exists")
            result = conn.execute(text("SELECT COUNT(*) FROM users"))
            user_count = result.fetchone()[0]
            print(f"Users count: {user_count}")
        else:
            print("✗ Users table does not exist")
        
        # Check videos table structure
        if 'videos' in tables:
            print("✓ Videos table exists")
            result = conn.execute(text("PRAGMA table_info(videos)"))
            columns = [row[1] for row in result]
            print("Videos columns:", columns)
            
            if 'user_id' in columns:
                print("✓ user_id column exists in videos")
            else:
                print("✗ user_id column missing in videos")
        else:
            print("✗ Videos table does not exist")

if __name__ == "__main__":
    check_database()
