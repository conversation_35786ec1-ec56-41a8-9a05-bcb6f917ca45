#!/bin/bash

echo "🚀 Starting tagTok User Authentication Migration"
echo "================================================"

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: Please run this script from the tagTok root directory"
    exit 1
fi

# Check if backend container is running
if ! docker-compose ps backend | grep -q "Up"; then
    echo "❌ Error: Backend container is not running. Please start the services first:"
    echo "   docker-compose up -d"
    exit 1
fi

echo "📋 Running database migration..."
echo ""

# Run the migration inside the backend container
docker-compose exec backend python migrations/add_user_authentication.py

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Migration completed successfully!"
    echo ""
    echo "🔐 Default login credentials:"
    echo "   Email: <EMAIL>"
    echo "   Password: admin123"
    echo "   Username: admin"
    echo ""
    echo "⚠️  IMPORTANT: Please change the default password after first login!"
    echo ""
    echo "🌐 You can now access the application at:"
    echo "   http://localhost:3001 (or your configured frontend port)"
    echo ""
else
    echo ""
    echo "❌ Migration failed. Please check the error messages above."
    exit 1
fi


