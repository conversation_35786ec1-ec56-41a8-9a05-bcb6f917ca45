# TagTok - TikTok Video Organization Platform

A self-hosted, containerized platform for downloading, analyzing, and organizing TikTok videos with AI-powered tagging and transcription. Now with **multi-tenant user authentication** - each user has their own private video collection.

## 🔐 New: Multi-User Authentication

TagTok now supports multiple users with complete data isolation:
- ✅ **User Registration & Login**: Create accounts and securely log in
- ✅ **Data Isolation**: Each user can only see their own videos and tags
- ✅ **Account Management**: Update profile, change password, delete account
- ✅ **Secure Authentication**: JWT-based authentication with password hashing
- ✅ **Password Recovery**: Forgot password functionality (ready for email integration)

## 🚀 Quick Start

### Automatic GPU Detection (Recommended)

The platform automatically detects your system's GPU capabilities and starts in the appropriate mode:

```bash
# Clone the repository
git clone <repository-url>
cd tagTok

# Start with automatic GPU detection
./start.sh
```

This script will:
- ✅ Detect if NVIDIA GPU is available
- ✅ Check NVIDIA Docker runtime
- ✅ Test GPU access in containers
- 🎯 Automatically choose GPU or CPU mode
- 🚀 Start all services with optimal configuration

### Manual Mode Selection

If you prefer manual control, you can choose the configuration:

#### CPU-Only Mode (Works on any system)
```bash
docker-compose up -d
```

#### GPU-Accelerated Mode (Requires NVIDIA GPU)
```bash
docker-compose -f docker-compose.gpu.yml up -d
```

## 🔧 System Requirements

### Minimum Requirements (CPU-Only)
- Docker and Docker Compose
- 4GB RAM
- 10GB free disk space
- Any modern CPU

### Recommended Requirements (GPU-Accelerated)
- NVIDIA GPU with 4GB+ VRAM
- NVIDIA drivers installed
- NVIDIA Container Toolkit
- 8GB+ RAM
- 20GB+ free disk space

## 🎯 GPU Support

### Automatic Detection
The platform automatically detects GPU availability:

```bash
./start.sh
```

**Output examples:**
```
🎉 Full GPU support detected! Starting with GPU acceleration...
📊 GPU Mode: true
📁 Compose File: docker-compose.gpu.yml
```

```
💻 GPU not available or not properly configured. Starting in CPU-only mode...
📊 GPU Mode: false
📁 Compose File: docker-compose.yml
```

### Manual GPU Setup

If you have an NVIDIA GPU but the automatic detection fails:

1. **Install NVIDIA drivers:**
   ```bash
   sudo apt update
   sudo apt install nvidia-driver-535  # or latest version
   ```

2. **Install NVIDIA Container Toolkit:**
   ```bash
   distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
   curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
   curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
   
   sudo apt update
   sudo apt install nvidia-container-toolkit
   sudo systemctl restart docker
   ```

3. **Verify installation:**
   ```bash
   ./check_gpu.sh
   ```

4. **Start with GPU support:**
   ```bash
   ./start.sh
   ```

## 🔐 Authentication Setup

After starting the services, you need to set up user authentication:

### 1. Run Database Migration
```bash
# Run the migration script to add user authentication
./run_migration.sh
```

This will:
- ✅ Create the users table
- ✅ Add user_id foreign keys to existing tables
- ✅ Create a default admin user
- ✅ Migrate existing data to the default user

### 2. Default Login Credentials
After migration, you can log in with:
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Username**: `admin`

⚠️ **Important**: Change the default password after first login!

### 3. Create Additional Users
- Use the registration page at `/register` to create new user accounts
- Each user will have their own private video collection
- Users cannot see each other's videos or tags

## 🌐 Access Points

Once started, access the platform at:

- **Frontend UI**: http://localhost:3001
- **Backend API**: http://localhost:8080
- **Ollama (LLM)**: http://localhost:11435
- **Nginx (Video serving)**: http://localhost:8790

## 📁 Directory Structure

```
tagTok/
├── backend/                 # FastAPI backend
├── frontend/               # React frontend
├── data/                   # Persistent data
│   ├── videos/            # Video files
│   ├── db/                # Database files
│   └── transcripts/       # Generated transcripts
├── docker-compose.yml     # CPU-only configuration
├── docker-compose.gpu.yml # GPU-accelerated configuration
├── start.sh              # Automatic startup script
├── check_gpu.sh          # GPU detection script
└── README.md
```

## 🔍 Troubleshooting

### GPU Issues

**Problem**: GPU not detected
```bash
# Check GPU status
./check_gpu.sh

# Verify NVIDIA Container Toolkit
docker run --rm --gpus all nvidia/cuda:11.8.0-base-ubuntu20.04 nvidia-smi
```

**Problem**: Docker can't access GPU
```bash
# Restart Docker after installing NVIDIA Container Toolkit
sudo systemctl restart docker

# Check Docker runtime
docker info | grep -i nvidia
```

### Performance Issues

**CPU Mode**: 
- Whisper transcription will be slower
- LLM inference will be slower
- Consider using smaller models

**GPU Mode**:
- Ensure sufficient VRAM (4GB+ recommended)
- Monitor GPU usage: `nvidia-smi`
- Consider using smaller models if VRAM is limited

### Service Issues

**Check service status:**
```bash
# View all services
docker-compose ps

# View logs
docker-compose logs -f [service-name]

# Restart specific service
docker-compose restart [service-name]
```

**Common fixes:**
```bash
# Full restart
docker-compose down
docker-compose up -d

# Rebuild containers
docker-compose build --no-cache
docker-compose up -d
```

## 🛠 Development

### Adding Videos

1. Place video files in `data/videos/`
2. Run the bootstrap script:
   ```bash
   docker-compose exec backend python bootstrap.py
   ```

### Environment Variables

Key environment variables:

```bash
# Backend
BACKEND_EXTERNAL_PORT=8080
BACKEND_INTERNAL_PORT=8000

# Frontend
FRONTEND_EXTERNAL_PORT=3001
FRONTEND_INTERNAL_PORT=80

# Ollama
OLLAMA_EXTERNAL_PORT=11435
OLLAMA_INTERNAL_PORT=11434

# Nginx
NGINX_PORT=8790

# AI Models
WHISPER_MODEL_SIZE=base  # tiny, base, small, medium, large
```

### Custom Models

To use different Ollama models:

1. **Pull a model:**
   ```bash
   docker-compose exec ollama ollama pull llama3.2:3b
   ```

2. **Update the model in docker-compose files:**
   ```yaml
   command: >
     sh -c "
       curl -X POST http://ollama:11434/api/pull -H 'Content-Type: application/json' -d '{\"name\": \"your-model:version\"}'
     "
   ```

## 📊 Performance Comparison

| Mode | Whisper Speed | LLM Speed | VRAM Usage | CPU Usage |
|------|---------------|-----------|------------|-----------|
| CPU  | ~1x (baseline) | ~1x (baseline) | 0GB | High |
| GPU  | ~5-10x faster | ~3-5x faster | 2-4GB | Low |

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test in both CPU and GPU modes
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
