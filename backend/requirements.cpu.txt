fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
pydantic==2.5.0
pydantic[email]==2.5.0
python-multipart==0.0.6
aiofiles==23.2.1
httpx==0.25.2
pydantic-settings==2.1.0
typing-extensions==4.8.0

# AI libs (CPU-friendly)
openai-whisper==20231117
# Use PyTorch CPU wheels from PyPI (no CUDA)
torch==2.1.1
# torchaudio CPU
torchaudio==2.1.1
# Smaller spaCy footprint; model downloaded at runtime if used
spacy==3.7.2
keybert==0.7.0
sentence-transformers==2.2.2
scikit-learn==1.3.2
# Use headless OpenCV to avoid X11/GUI deps
opencv-python-headless==********
Pillow==10.1.0
moviepy==1.0.3
huggingface-hub==0.16.4

# Tools
yt-dlp>=2024.10.7
rich>=13.7.1

