#!/bin/bash

echo "=== TagTok Startup Script ==="
echo "Detecting system configuration..."

# Function to check if NVIDIA GPU is available
check_nvidia_gpu() {
    if command -v nvidia-smi &> /dev/null; then
        if nvidia-smi &> /dev/null; then
            echo "✅ NVIDIA GPU detected"
            return 0
        else
            echo "❌ NVIDIA drivers found but GPU not accessible"
            return 1
        fi
    else
        echo "❌ No NVIDIA drivers found"
        return 1
    fi
}

# Function to check if NVIDIA Docker runtime is available
check_nvidia_docker() {
    if docker info | grep -q "nvidia"; then
        echo "✅ NVIDIA Docker runtime available"
        return 0
    else
        echo "❌ NVIDIA Docker runtime not available"
        return 1
    fi
}

# Function to test GPU access in Docker
test_docker_gpu() {
    if docker run --rm --gpus all nvidia/cuda:11.8.0-base-ubuntu20.04 nvidia-smi &> /dev/null; then
        echo "✅ Docker can access GPU"
        return 0
    else
        echo "❌ Docker cannot access GPU"
        return 1
    fi
}

# Check GPU availability
echo "1. Checking NVIDIA GPU availability..."
if check_nvidia_gpu && check_nvidia_docker && test_docker_gpu; then
    echo "🎉 Full GPU support detected! Starting with GPU acceleration..."
    COMPOSE_FILE="docker-compose.gpu.yml"
    GPU_MODE=true
else
    echo "💻 GPU not available or not properly configured. Starting in CPU-only mode..."
    COMPOSE_FILE="docker-compose.yml"
    GPU_MODE=false
fi

echo
echo "2. Starting services with $COMPOSE_FILE..."

# Set environment variables based on mode
if [ "$GPU_MODE" = true ]; then
    export NVIDIA_VISIBLE_DEVICES=all
    export NVIDIA_DRIVER_CAPABILITIES=compute,utility
    echo "   GPU mode: NVIDIA_VISIBLE_DEVICES=all"
else
    unset NVIDIA_VISIBLE_DEVICES
    unset NVIDIA_DRIVER_CAPABILITIES
    echo "   CPU mode: No GPU acceleration"
fi

echo
echo "3. Starting Docker Compose..."

# Stop any existing containers
echo "   Stopping existing containers..."
docker-compose down

# Start with the appropriate configuration
echo "   Starting with $COMPOSE_FILE..."
docker-compose -f $COMPOSE_FILE up -d

echo
echo "4. Waiting for services to be ready..."
sleep 10

# Check service status
echo "   Checking service status..."
docker-compose -f $COMPOSE_FILE ps

echo
echo "=== Startup Complete ==="
echo "🌐 Frontend: http://localhost:3001"
echo "🔧 Backend API: http://localhost:8080"
echo "🤖 Ollama: http://localhost:11435"
echo "📺 Nginx: http://localhost:8790"
echo
echo "📊 GPU Mode: $GPU_MODE"
echo "📁 Compose File: $COMPOSE_FILE"
echo
echo "To view logs: docker-compose -f $COMPOSE_FILE logs -f"
echo "To stop: docker-compose -f $COMPOSE_FILE down"
