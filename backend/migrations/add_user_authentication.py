#!/usr/bin/env python3
"""
Migration script to add user authentication and multi-tenancy support.
This script will:
1. Create the users table
2. Add user_id foreign keys to existing tables
3. Update existing data to assign to a default user
"""

import os
import sys
from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, DateTime, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from datetime import datetime

# Add the parent directory to the path so we can import our models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import DATABASE_URL, engine, Base, User
from utils.auth import get_password_hash

def create_default_user():
    """Create a default user for existing data."""
    from sqlalchemy.orm import sessionmaker
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Check if default user already exists
        default_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if not default_user:
            # Create default user
            default_user = User(
                email="<EMAIL>",
                username="admin",
                full_name="Default Administrator",
                hashed_password=get_password_hash("admin123"),
                is_active=True,
                is_verified=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            db.add(default_user)
            db.commit()
            db.refresh(default_user)
            print(f"Created default user: {default_user.email}")
        else:
            print(f"Default user already exists: {default_user.email}")
        
        return default_user.id
    except Exception as e:
        print(f"Error creating default user: {e}")
        db.rollback()
        return None
    finally:
        db.close()

def migrate_sqlite_tables():
    """Migrate SQLite tables by recreating them with new schema."""
    try:
        with engine.connect() as conn:
            # Check if we need to migrate
            result = conn.execute(text("PRAGMA table_info(videos)"))
            columns = [row[1] for row in result]
            
            if 'user_id' in columns:
                print("✓ Tables already have user_id columns")
                return True
            
            print("🔄 Migrating tables to add user_id columns...")
            
            # Create new tables with user_id columns
            Base.metadata.create_all(bind=engine)
            print("✓ New tables created with user_id columns")
            
            # Note: In a production environment, you would want to:
            # 1. Backup existing data
            # 2. Migrate data from old tables to new tables
            # 3. Drop old tables
            # 4. Rename new tables
            
            # For now, we'll just create the new schema
            # Existing data will need to be re-imported or manually migrated
            
            return True
            
    except Exception as e:
        print(f"Error migrating tables: {e}")
        return False

def run_migration():
    """Run the complete migration."""
    print("Starting user authentication migration...")
    
    try:
        # Create all tables (this will create the users table and add user_id columns)
        Base.metadata.create_all(bind=engine)
        print("✓ Database schema updated")
        
        # Create default user
        default_user_id = create_default_user()
        if not default_user_id:
            print("❌ Failed to create default user")
            return False
        
        # Migrate tables if needed
        if not migrate_sqlite_tables():
            print("❌ Failed to migrate tables")
            return False
        
        print("✓ Migration completed successfully!")
        print(f"Default user credentials:")
        print(f"  Email: <EMAIL>")
        print(f"  Password: admin123")
        print(f"  Username: admin")
        print("\n⚠️  Please change the default password after first login!")
        print("\n📝 Note: Existing data may need to be re-imported due to schema changes.")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)


