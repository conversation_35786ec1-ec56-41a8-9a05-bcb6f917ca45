#!/usr/bin/env python3
"""
Fix database tables by dropping old tables and recreating with correct schema.
"""

import os
import sys
from sqlalchemy import create_engine, text, MetaData
from datetime import datetime

# Add the parent directory to the path so we can import our models
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import engine, Base, User
from utils.auth import get_password_hash

def fix_database():
    """Fix the database by recreating tables with correct schema."""
    try:
        with engine.connect() as conn:
            # Drop all existing tables
            print("🗑️  Dropping existing tables...")
            conn.execute(text("DROP TABLE IF EXISTS video_tags"))
            conn.execute(text("DROP TABLE IF EXISTS recipes"))
            conn.execute(text("DROP TABLE IF EXISTS processing_jobs"))
            conn.execute(text("DROP TABLE IF EXISTS videos"))
            conn.execute(text("DROP TABLE IF EXISTS tags"))
            conn.execute(text("DROP TABLE IF EXISTS users"))
            conn.commit()
            print("✓ Old tables dropped")
            
            # Create new tables with correct schema
            print("🔨 Creating new tables with correct schema...")
            Base.metadata.create_all(bind=engine)
            print("✓ New tables created")
            
            # Create default user
            print("👤 Creating default user...")
            default_user = User(
                email="<EMAIL>",
                username="admin",
                full_name="Default Administrator",
                hashed_password=get_password_hash("admin123"),
                is_active=True,
                is_verified=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            from sqlalchemy.orm import sessionmaker
            SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
            db = SessionLocal()
            db.add(default_user)
            db.commit()
            db.refresh(default_user)
            db.close()
            
            print("✓ Default user created")
            print("\n🎉 Database fixed successfully!")
            print(f"Default credentials:")
            print(f"  Email: <EMAIL>")
            print(f"  Password: admin123")
            print(f"  Username: admin")
            
            return True
            
    except Exception as e:
        print(f"❌ Error fixing database: {e}")
        return False

if __name__ == "__main__":
    success = fix_database()
    sys.exit(0 if success else 1)
