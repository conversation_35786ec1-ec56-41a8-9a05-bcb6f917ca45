#!/bin/bash

echo "=== GPU and Docker Runtime Check ==="
echo

# Check if NVIDIA drivers are installed
echo "1. Checking NVIDIA drivers..."
if command -v nvidia-smi &> /dev/null; then
    echo "✅ NVIDIA drivers found"
    nvidia-smi --query-gpu=name,driver_version,memory.total --format=csv,noheader,nounits
else
    echo "❌ NVIDIA drivers not found"
fi
echo

# Check if NVIDIA Docker runtime is installed
echo "2. Checking NVIDIA Docker runtime..."
if docker info | grep -q "nvidia"; then
    echo "✅ NVIDIA Docker runtime found"
else
    echo "❌ NVIDIA Docker runtime not found"
    echo "   Install with: sudo apt-get install nvidia-container-toolkit"
fi
echo

# Check if Docker can access GPU
echo "3. Testing Docker GPU access..."
if docker run --rm --gpus all nvidia/cuda:11.8.0-base-ubuntu20.04 nvidia-smi &> /dev/null; then
    echo "✅ Docker can access GPU"
else
    echo "❌ Docker cannot access GPU"
    echo "   This might be due to:"
    echo "   - NVIDIA Container Toolkit not installed"
    echo "   - Docker daemon not restarted after installation"
    echo "   - Incorrect runtime configuration"
fi
echo

# Check CUDA version
echo "4. Checking CUDA version..."
if command -v nvcc &> /dev/null; then
    echo "✅ CUDA compiler found"
    nvcc --version | grep "release"
else
    echo "❌ CUDA compiler not found"
fi
echo

echo "=== Recommendations ==="
if ! docker info | grep -q "nvidia"; then
    echo "1. Install NVIDIA Container Toolkit:"
    echo "   sudo apt-get install nvidia-container-toolkit"
    echo "   sudo systemctl restart docker"
    echo
fi

if ! docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi &> /dev/null; then
    echo "2. After installing NVIDIA Container Toolkit, restart Docker:"
    echo "   sudo systemctl restart docker"
    echo
fi

echo "3. Rebuild your containers with GPU support:"
echo "   docker-compose down"
echo "   docker-compose build --no-cache"
echo "   docker-compose up -d"
